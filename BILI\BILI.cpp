﻿// BILI.cpp : 定义应用程序的入口点。
//

#include "framework.h"
#include "BILI.h"

#define MAX_LOADSTRING 100

// 全局变量:
HINSTANCE hInst;                                // 当前实例
WCHAR szTitle[MAX_LOADSTRING];                  // 标题栏文本
WCHAR szWindowClass[MAX_LOADSTRING];            // 主窗口类名

// 模拟器相关全局变量
BiliViewSimulator* g_simulator = nullptr;
bool g_isRunning = false;
std::thread* g_workerThread = nullptr;

// 控件句柄
HWND g_hEditBvid = nullptr;
HWND g_hEditCount = nullptr;
HWND g_hEditDelay = nullptr;
HWND g_hButtonStart = nullptr;
HWND g_hButtonStop = nullptr;
HWND g_hStaticStatus = nullptr;

// 此代码模块中包含的函数的前向声明:
ATOM                MyRegisterClass(HINSTANCE hInstance);
BOOL                InitInstance(HINSTANCE, int);
LRESULT CALLBACK    WndProc(HWND, UINT, WPARAM, LPARAM);
INT_PTR CALLBACK    About(HWND, UINT, WPARAM, LPARAM);

// 新增函数声明
void CreateControls(HWND hWnd);
void StartSimulation();
void StopSimulation();
void WorkerThread(std::string bvid, int count, int delay);
void UpdateStatus(const std::wstring& status);
std::string WStringToString(const std::wstring& wstr);
std::wstring StringToWString(const std::string& str);

int APIENTRY wWinMain(_In_ HINSTANCE hInstance,
                     _In_opt_ HINSTANCE hPrevInstance,
                     _In_ LPWSTR    lpCmdLine,
                     _In_ int       nCmdShow)
{
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);

    // TODO: 在此处放置代码。

    // 初始化全局字符串
    LoadStringW(hInstance, IDS_APP_TITLE, szTitle, MAX_LOADSTRING);
    LoadStringW(hInstance, IDC_BILI, szWindowClass, MAX_LOADSTRING);
    MyRegisterClass(hInstance);

    // 执行应用程序初始化:
    if (!InitInstance (hInstance, nCmdShow))
    {
        return FALSE;
    }

    HACCEL hAccelTable = LoadAccelerators(hInstance, MAKEINTRESOURCE(IDC_BILI));

    MSG msg;

    // 主消息循环:
    while (GetMessage(&msg, nullptr, 0, 0))
    {
        if (!TranslateAccelerator(msg.hwnd, hAccelTable, &msg))
        {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }

    return (int) msg.wParam;
}



//
//  函数: MyRegisterClass()
//
//  目标: 注册窗口类。
//
ATOM MyRegisterClass(HINSTANCE hInstance)
{
    WNDCLASSEXW wcex;

    wcex.cbSize = sizeof(WNDCLASSEX);

    wcex.style          = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc    = WndProc;
    wcex.cbClsExtra     = 0;
    wcex.cbWndExtra     = 0;
    wcex.hInstance      = hInstance;
    wcex.hIcon          = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_BILI));
    wcex.hCursor        = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground  = (HBRUSH)(COLOR_WINDOW+1);
    wcex.lpszMenuName   = MAKEINTRESOURCEW(IDC_BILI);
    wcex.lpszClassName  = szWindowClass;
    wcex.hIconSm        = LoadIcon(wcex.hInstance, MAKEINTRESOURCE(IDI_SMALL));

    return RegisterClassExW(&wcex);
}

//
//   函数: InitInstance(HINSTANCE, int)
//
//   目标: 保存实例句柄并创建主窗口
//
//   注释:
//
//        在此函数中，我们在全局变量中保存实例句柄并
//        创建和显示主程序窗口。
//
BOOL InitInstance(HINSTANCE hInstance, int nCmdShow)
{
   hInst = hInstance; // 将实例句柄存储在全局变量中

   // 创建固定大小的窗口
   HWND hWnd = CreateWindowW(szWindowClass, szTitle, WS_OVERLAPPEDWINDOW & ~WS_THICKFRAME & ~WS_MAXIMIZEBOX,
      CW_USEDEFAULT, 0, 500, 400, nullptr, nullptr, hInstance, nullptr);

   if (!hWnd)
   {
      return FALSE;
   }

   // 初始化模拟器
   g_simulator = new BiliViewSimulator();
   if (!g_simulator->initialize()) {
       delete g_simulator;
       g_simulator = nullptr;
       MessageBoxW(hWnd, L"网络初始化失败", L"错误", MB_OK | MB_ICONERROR);
   }

   ShowWindow(hWnd, nCmdShow);
   UpdateWindow(hWnd);

   return TRUE;
}

//
//  函数: WndProc(HWND, UINT, WPARAM, LPARAM)
//
//  目标: 处理主窗口的消息。
//
//  WM_COMMAND  - 处理应用程序菜单
//  WM_PAINT    - 绘制主窗口
//  WM_DESTROY  - 发送退出消息并返回
//
//
LRESULT CALLBACK WndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case WM_CREATE:
        CreateControls(hWnd);
        break;
    case WM_COMMAND:
        {
            int wmId = LOWORD(wParam);
            // 分析菜单选择:
            switch (wmId)
            {
            case IDM_ABOUT:
                DialogBox(hInst, MAKEINTRESOURCE(IDD_ABOUTBOX), hWnd, About);
                break;
            case IDM_EXIT:
                DestroyWindow(hWnd);
                break;
            case IDC_BUTTON_START:
                StartSimulation();
                break;
            case IDC_BUTTON_STOP:
                StopSimulation();
                break;
            default:
                return DefWindowProc(hWnd, message, wParam, lParam);
            }
        }
        break;
    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);
            // TODO: 在此处添加使用 hdc 的任何绘图代码...
            EndPaint(hWnd, &ps);
        }
        break;
    case WM_DESTROY:
        StopSimulation();
        if (g_simulator) {
            delete g_simulator;
            g_simulator = nullptr;
        }
        PostQuitMessage(0);
        break;
    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// “关于”框的消息处理程序。
INT_PTR CALLBACK About(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    UNREFERENCED_PARAMETER(lParam);
    switch (message)
    {
    case WM_INITDIALOG:
        return (INT_PTR)TRUE;

    case WM_COMMAND:
        if (LOWORD(wParam) == IDOK || LOWORD(wParam) == IDCANCEL)
        {
            EndDialog(hDlg, LOWORD(wParam));
            return (INT_PTR)TRUE;
        }
        break;
    }
    return (INT_PTR)FALSE;
}

// 创建控件
void CreateControls(HWND hWnd) {
    // BV号标签和输入框
    CreateWindowW(L"STATIC", L"BV号:", WS_VISIBLE | WS_CHILD,
        20, 20, 80, 20, hWnd, (HMENU)IDC_STATIC_BVID, hInst, nullptr);

    g_hEditBvid = CreateWindowW(L"EDIT", L"BV1234567890", WS_VISIBLE | WS_CHILD | WS_BORDER,
        110, 18, 300, 25, hWnd, (HMENU)IDC_EDIT_BVID, hInst, nullptr);

    // 次数标签和输入框
    CreateWindowW(L"STATIC", L"模拟次数:", WS_VISIBLE | WS_CHILD,
        20, 60, 80, 20, hWnd, (HMENU)IDC_STATIC_COUNT, hInst, nullptr);

    g_hEditCount = CreateWindowW(L"EDIT", L"10", WS_VISIBLE | WS_CHILD | WS_BORDER,
        110, 58, 100, 25, hWnd, (HMENU)IDC_EDIT_COUNT, hInst, nullptr);

    // 延迟标签和输入框
    CreateWindowW(L"STATIC", L"延迟(秒):", WS_VISIBLE | WS_CHILD,
        230, 60, 80, 20, hWnd, (HMENU)IDC_STATIC_DELAY, hInst, nullptr);

    g_hEditDelay = CreateWindowW(L"EDIT", L"30", WS_VISIBLE | WS_CHILD | WS_BORDER,
        320, 58, 90, 25, hWnd, (HMENU)IDC_EDIT_DELAY, hInst, nullptr);

    // 开始按钮
    g_hButtonStart = CreateWindowW(L"BUTTON", L"开始模拟", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        20, 100, 100, 35, hWnd, (HMENU)IDC_BUTTON_START, hInst, nullptr);

    // 停止按钮
    g_hButtonStop = CreateWindowW(L"BUTTON", L"停止模拟", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        140, 100, 100, 35, hWnd, (HMENU)IDC_BUTTON_STOP, hInst, nullptr);

    // 状态显示
    g_hStaticStatus = CreateWindowW(L"STATIC", L"状态: 就绪", WS_VISIBLE | WS_CHILD,
        20, 150, 400, 200, hWnd, (HMENU)IDC_STATIC_STATUS, hInst, nullptr);

    // 初始状态：停止按钮禁用
    EnableWindow(g_hButtonStop, FALSE);
}

// 字符串转换函数
std::string WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

std::wstring StringToWString(const std::string& str) {
    if (str.empty()) return std::wstring();
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

// 更新状态显示
void UpdateStatus(const std::wstring& status) {
    if (g_hStaticStatus) {
        SetWindowTextW(g_hStaticStatus, (L"状态: " + status).c_str());
    }
}

// 开始模拟
void StartSimulation() {
    if (g_isRunning || !g_simulator) {
        return;
    }

    // 获取输入参数
    wchar_t bvidBuffer[256];
    wchar_t countBuffer[32];
    wchar_t delayBuffer[32];

    GetWindowTextW(g_hEditBvid, bvidBuffer, 256);
    GetWindowTextW(g_hEditCount, countBuffer, 32);
    GetWindowTextW(g_hEditDelay, delayBuffer, 32);

    std::string bvid = WStringToString(bvidBuffer);
    int count = _wtoi(countBuffer);
    int delay = _wtoi(delayBuffer);

    // 验证输入
    if (bvid.empty() || bvid.length() < 10) {
        MessageBoxW(nullptr, L"请输入有效的BV号", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    if (count <= 0 || count > 1000) {
        MessageBoxW(nullptr, L"模拟次数应在1-1000之间", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    if (delay < 5 || delay > 3600) {
        MessageBoxW(nullptr, L"延迟时间应在5-3600秒之间", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 启动工作线程
    g_isRunning = true;
    EnableWindow(g_hButtonStart, FALSE);
    EnableWindow(g_hButtonStop, TRUE);

    UpdateStatus(L"正在启动模拟...");

    g_workerThread = new std::thread(WorkerThread, bvid, count, delay);
}

// 停止模拟
void StopSimulation() {
    if (!g_isRunning) {
        return;
    }

    g_isRunning = false;
    UpdateStatus(L"正在停止...");

    // 等待工作线程结束
    if (g_workerThread) {
        if (g_workerThread->joinable()) {
            g_workerThread->join();
        }
        delete g_workerThread;
        g_workerThread = nullptr;
    }

    EnableWindow(g_hButtonStart, TRUE);
    EnableWindow(g_hButtonStop, FALSE);
    UpdateStatus(L"已停止");
}

// 工作线程函数
void WorkerThread(std::string bvid, int count, int delay) {
    int completed = 0;

    for (int i = 0; i < count && g_isRunning; i++) {
        // 更新状态
        std::wstring status = L"正在模拟第 " + std::to_wstring(i + 1) + L"/" + std::to_wstring(count) + L" 次观看...";
        UpdateStatus(status);

        // 执行模拟观看
        bool success = g_simulator->simulateVideoView(bvid);

        if (success) {
            completed++;
        }

        // 如果不是最后一次且仍在运行，等待延迟时间
        if (i < count - 1 && g_isRunning) {
            for (int j = 0; j < delay && g_isRunning; j++) {
                std::this_thread::sleep_for(std::chrono::seconds(1));

                // 更新倒计时状态
                std::wstring countdownStatus = L"完成 " + std::to_wstring(completed) + L"/" + std::to_wstring(count) +
                                             L" 次，等待 " + std::to_wstring(delay - j) + L" 秒...";
                UpdateStatus(countdownStatus);
            }
        }
    }

    // 完成或被中断
    if (g_isRunning) {
        std::wstring finalStatus = L"模拟完成！成功 " + std::to_wstring(completed) + L"/" + std::to_wstring(count) + L" 次";
        UpdateStatus(finalStatus);

        // 自动停止
        g_isRunning = false;
        EnableWindow(g_hButtonStart, TRUE);
        EnableWindow(g_hButtonStop, FALSE);
    }
}
