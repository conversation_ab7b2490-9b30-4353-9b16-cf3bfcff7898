#pragma once
#include "framework.h"

class BiliViewSimulator {
private:
    HINTERNET hInternet;
    HINTERNET hConnect;
    std::string userAgent;
    std::vector<std::string> userAgents;
    std::random_device rd;
    std::mt19937 gen;

    std::string getRandomUserAgent();
    std::string generateDeviceInfo();
    bool sendHttpRequest(const std::string& url, const std::string& method = "GET",
                        const std::string& postData = "", const std::string& headers = "");
    bool sendHeartbeat(const std::string& bvid, int currentTime);
    bool reportPlayProgress(const std::string& bvid, int currentTime, int duration);
    int generateRandomPlayTime(int videoDuration);

public:
    BiliViewSimulator();
    ~BiliViewSimulator();

    bool initialize();
    bool simulateVideoView(const std::string& bvid, int duration = 0);
    bool batchSimulateViews(const std::string& bvid, int count, int delaySeconds = 30);
    void cleanup();
    bool setProxy(const std::string& proxyServer, int port);
};
