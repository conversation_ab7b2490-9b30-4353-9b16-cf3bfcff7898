#include "BiliViewSimulator.h"

BiliViewSimulator::BiliViewSimulator() : hInternet(nullptr), hConnect(nullptr), gen(rd()) {
    userAgents = {
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
    };
}

BiliViewSimulator::~BiliViewSimulator() {
    cleanup();
}

bool BiliViewSimulator::initialize() {
    userAgent = getRandomUserAgent();
    hInternet = InternetOpenA(userAgent.c_str(), INTERNET_OPEN_TYPE_DIRECT, nullptr, nullptr, 0);
    
    if (!hInternet) {
        return false;
    }
    
    return true;
}

std::string BiliViewSimulator::getRandomUserAgent() {
    std::uniform_int_distribution<> dis(0, static_cast<int>(userAgents.size()) - 1);
    return userAgents[dis(gen)];
}

std::string BiliViewSimulator::generateDeviceInfo() {
    std::uniform_int_distribution<> screenWidth(1920, 3840);
    std::uniform_int_distribution<> screenHeight(1080, 2160);
    std::uniform_int_distribution<> colorDepth(24, 32);
    
    std::stringstream ss;
    ss << "screen=" << screenWidth(gen) << "x" << screenHeight(gen) 
       << "&color_depth=" << colorDepth(gen);
    return ss.str();
}

bool BiliViewSimulator::sendHttpRequest(const std::string& url, const std::string& method, 
                                       const std::string& postData, const std::string& headers) {
    std::string hostname, path;
    size_t protocolEnd = url.find("://");
    if (protocolEnd == std::string::npos) return false;
    
    size_t hostStart = protocolEnd + 3;
    size_t pathStart = url.find("/", hostStart);
    
    if (pathStart == std::string::npos) {
        hostname = url.substr(hostStart);
        path = "/";
    } else {
        hostname = url.substr(hostStart, pathStart - hostStart);
        path = url.substr(pathStart);
    }
    
    HINTERNET hConnectLocal = InternetConnectA(hInternet, hostname.c_str(), 
                                         INTERNET_DEFAULT_HTTPS_PORT, 
                                         nullptr, nullptr, INTERNET_SERVICE_HTTP, 0, 0);
    if (!hConnectLocal) {
        return false;
    }
    
    DWORD flags = INTERNET_FLAG_SECURE | INTERNET_FLAG_NO_CACHE_WRITE | INTERNET_FLAG_RELOAD;
    HINTERNET hRequest = HttpOpenRequestA(hConnectLocal, method.c_str(), path.c_str(), 
                                         nullptr, nullptr, nullptr, flags, 0);
    if (!hRequest) {
        InternetCloseHandle(hConnectLocal);
        return false;
    }
    
    std::string allHeaders = "Accept: */*\r\n";
    allHeaders += "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8\r\n";
    allHeaders += "Accept-Encoding: gzip, deflate, br\r\n";
    allHeaders += "Connection: keep-alive\r\n";
    allHeaders += "Referer: https://www.bilibili.com/\r\n";
    allHeaders += "Origin: https://www.bilibili.com\r\n";
    if (!headers.empty()) {
        allHeaders += headers + "\r\n";
    }
    
    bool result = HttpSendRequestA(hRequest, allHeaders.c_str(), static_cast<DWORD>(allHeaders.length()),
                                  (LPVOID)postData.c_str(), static_cast<DWORD>(postData.length()));
    
    if (result) {
        char buffer[4096];
        DWORD bytesRead;
        while (InternetReadFile(hRequest, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
            // Process response data
        }
    }
    
    InternetCloseHandle(hRequest);
    InternetCloseHandle(hConnectLocal);
    
    return result;
}

bool BiliViewSimulator::sendHeartbeat(const std::string& bvid, int currentTime) {
    std::string url = "https://api.bilibili.com/x/click-interface/web/heartbeat";
    
    std::stringstream postData;
    postData << "bvid=" << bvid 
             << "&played_time=" << currentTime
             << "&realtime=" << currentTime
             << "&start_ts=" << (std::chrono::duration_cast<std::chrono::seconds>(
                 std::chrono::system_clock::now().time_since_epoch()).count() - currentTime)
             << "&type=3&sub_type=0";
    
    std::string headers = "Content-Type: application/x-www-form-urlencoded";
    
    return sendHttpRequest(url, "POST", postData.str(), headers);
}

bool BiliViewSimulator::reportPlayProgress(const std::string& bvid, int currentTime, int duration) {
    std::string url = "https://api.bilibili.com/x/click-interface/web/progress";
    
    std::stringstream postData;
    postData << "bvid=" << bvid 
             << "&cid=0"
             << "&progress=" << currentTime
             << "&duration=" << duration
             << "&type=3";
    
    std::string headers = "Content-Type: application/x-www-form-urlencoded";
    
    return sendHttpRequest(url, "POST", postData.str(), headers);
}

int BiliViewSimulator::generateRandomPlayTime(int videoDuration) {
    if (videoDuration <= 0) {
        std::uniform_int_distribution<> dis(30, 600);
        return dis(gen);
    }
    
    std::uniform_real_distribution<> dis(0.3, 0.95);
    return static_cast<int>(videoDuration * dis(gen));
}

bool BiliViewSimulator::simulateVideoView(const std::string& bvid, int duration) {
    if (!hInternet) {
        return false;
    }
    
    int playTime = generateRandomPlayTime(duration);
    
    std::string videoUrl = "https://www.bilibili.com/video/" + bvid;
    if (!sendHttpRequest(videoUrl, "GET")) {
        return false;
    }
    
    int heartbeatInterval = 30;
    int currentTime = 0;
    
    while (currentTime < playTime) {
        std::this_thread::sleep_for(std::chrono::seconds(heartbeatInterval));
        currentTime += heartbeatInterval;
        
        if (!sendHeartbeat(bvid, currentTime)) {
            // Continue on heartbeat failure
        }
        
        if (currentTime % 120 == 0) {
            reportPlayProgress(bvid, currentTime, duration > 0 ? duration : playTime);
        }
    }
    
    reportPlayProgress(bvid, playTime, duration > 0 ? duration : playTime);
    
    return true;
}

bool BiliViewSimulator::batchSimulateViews(const std::string& bvid, int count, int delaySeconds) {
    for (int i = 0; i < count; i++) {
        userAgent = getRandomUserAgent();
        
        if (!simulateVideoView(bvid)) {
            continue;
        }
        
        if (i < count - 1) {
            std::uniform_real_distribution<> delayVariation(0.5, 1.5);
            int actualDelay = static_cast<int>(delaySeconds * delayVariation(gen));
            std::this_thread::sleep_for(std::chrono::seconds(actualDelay));
        }
    }
    
    return true;
}

void BiliViewSimulator::cleanup() {
    if (hConnect) {
        InternetCloseHandle(hConnect);
        hConnect = nullptr;
    }
    if (hInternet) {
        InternetCloseHandle(hInternet);
        hInternet = nullptr;
    }
}

bool BiliViewSimulator::setProxy(const std::string& proxyServer, int port) {
    cleanup();
    
    std::string proxyInfo = proxyServer + ":" + std::to_string(port);
    hInternet = InternetOpenA(userAgent.c_str(), INTERNET_OPEN_TYPE_PROXY, 
                             proxyInfo.c_str(), nullptr, 0);
    
    return hInternet != nullptr;
}
