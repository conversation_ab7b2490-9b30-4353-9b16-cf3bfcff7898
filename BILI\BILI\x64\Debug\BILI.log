﻿  BILI.cpp
  BiliViewSimulator.cpp
D:\B-刷播放\BILI\BiliViewSimulator.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“BILI.cpp”)
  
D:\B-刷播放\BILI\BILI.cpp(133,22): error C2039: "initialize": 不是 "BiliViewSimulator" 的成员
      D:\B-刷播放\BILI\BiliViewSimulator.h(4,7):
      参见“BiliViewSimulator”的声明
  
D:\B-刷播放\BILI\BiliViewSimulator.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“BiliViewSimulator.cpp”)
  
D:\B-刷播放\BILI\BiliViewSimulator.cpp(18,25): error C2039: "initialize": 不是 "BiliViewSimulator" 的成员
      D:\B-刷播放\BILI\BiliViewSimulator.h(4,7):
      参见“BiliViewSimulator”的声明
  
D:\B-刷播放\BILI\BiliViewSimulator.cpp(19,5): error C2065: “userAgent”: 未声明的标识符
D:\B-刷播放\BILI\BiliViewSimulator.cpp(19,17): error C3861: “getRandomUserAgent”: 找不到标识符
D:\B-刷播放\BILI\BiliViewSimulator.cpp(20,5): error C2065: “hInternet”: 未声明的标识符
D:\B-刷播放\BILI\BiliViewSimulator.cpp(20,31): error C2065: “userAgent”: 未声明的标识符
D:\B-刷播放\BILI\BiliViewSimulator.cpp(22,10): error C2065: “hInternet”: 未声明的标识符
  正在生成代码...
