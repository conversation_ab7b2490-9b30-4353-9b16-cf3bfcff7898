# B站播放量模拟器

## ⚠️ 重要声明

**本项目仅供学习和研究目的使用。模拟虚假播放量可能违反B站服务条款，使用者需自行承担相关风险。**

## 功能特性

- 模拟真实用户观看B站视频
- 支持批量模拟观看
- 随机User-Agent和设备信息
- 模拟真实的观看时长和心跳请求
- 可配置的延迟时间避免被检测

## 使用方法

1. **编译项目**
   - 使用Visual Studio 2022打开`BILI/BILI.sln`
   - 选择Release x64配置
   - 编译项目

2. **运行程序**
   - 运行生成的exe文件
   - 在界面中输入以下信息：
     - **BV号**: 要模拟观看的视频BV号（如：BV1234567890）
     - **模拟次数**: 要模拟的观看次数（1-1000）
     - **延迟(秒)**: 每次模拟之间的延迟时间（5-3600秒）

3. **开始模拟**
   - 点击"开始模拟"按钮
   - 程序会显示当前进度和状态
   - 可以随时点击"停止模拟"按钮中断操作

## 技术实现

### 核心功能

1. **网络请求模拟**
   - 使用WinINet API发送HTTP/HTTPS请求
   - 模拟真实浏览器的请求头和参数

2. **观看行为模拟**
   - 随机生成观看时长（视频总时长的30%-95%）
   - 定期发送心跳请求（每30秒）
   - 上报播放进度（每2分钟）

3. **反检测机制**
   - 随机User-Agent轮换
   - 随机设备信息生成
   - 可配置的请求间隔
   - 模拟真实的观看时长分布

### 主要文件

- `BILI.cpp` - 主程序和界面逻辑
- `BiliViewSimulator.h/cpp` - 核心模拟器类
- `framework.h` - 系统头文件和依赖

## 注意事项

1. **合法使用**: 请确保遵守相关法律法规和平台服务条款
2. **适度使用**: 避免过于频繁的请求，以免被平台检测
3. **网络环境**: 建议在稳定的网络环境下使用
4. **代理支持**: 如需要可以配置代理服务器

## 免责声明

本项目仅用于技术学习和研究，开发者不对使用本项目造成的任何后果承担责任。使用者应当：

- 遵守当地法律法规
- 遵守平台服务条款
- 承担使用风险
- 不得用于商业目的或恶意行为

## 技术支持

如有技术问题，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件到开发者邮箱

---

**再次提醒：请合法合规使用本项目，开发者不承担任何使用风险！**
