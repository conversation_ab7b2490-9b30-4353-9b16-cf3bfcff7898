﻿#pragma once

#include "resource.h"
#include "BiliViewSimulator.h"

// 控件ID定义
#define IDC_EDIT_BVID       1001
#define IDC_EDIT_COUNT      1002
#define IDC_EDIT_DELAY      1003
#define IDC_BUTTON_START    1004
#define IDC_BUTTON_STOP     1005
#define IDC_STATIC_STATUS   1006
#define IDC_STATIC_BVID     1007
#define IDC_STATIC_COUNT    1008
#define IDC_STATIC_DELAY    1009

// 全局变量
extern BiliViewSimulator* g_simulator;
extern bool g_isRunning;
